{"name": "firesplash-mvp", "private": true, "version": "1.3.0", "type": "module", "scripts": {"dev": "vite", "build": "vite build", "lint": "eslint .", "preview": "vite preview", "version:sync": "node scripts/update-version.js", "version:update": "node scripts/update-version.js"}, "dependencies": {"lucide-react": "^0.344.0", "react": "^18.3.1", "react-dom": "^18.3.1", "@supabase/supabase-js": "^2.39.0", "leaflet": "^1.9.4"}, "devDependencies": {"@eslint/js": "^9.9.1", "@types/react": "^18.3.5", "@types/react-dom": "^18.3.0", "@types/leaflet": "^1.9.8", "@vitejs/plugin-react": "^4.3.1", "autoprefixer": "^10.4.18", "eslint": "^9.9.1", "eslint-plugin-react-hooks": "^5.1.0-rc.0", "eslint-plugin-react-refresh": "^0.4.11", "globals": "^15.9.0", "postcss": "^8.4.35", "tailwindcss": "^3.4.1", "typescript": "^5.5.3", "typescript-eslint": "^8.3.0", "vite": "^5.4.2"}}