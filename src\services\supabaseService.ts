import { supabase } from '../lib/supabase';
import { Commune, PlanEau, BaseDash } from '../types';

/**
 * Service pour récupérer les données depuis Supabase
 */

export async function getCommunes(): Promise<Commune[]> {
  try {
    const { data, error } = await supabase
      .from('communes')
      .select('*')
      .order('nom');

    if (error) {
      console.error('Erreur lors de la récupération des communes:', error);
      return [];
    }

    return data.map(commune => ({
      id: commune.id,
      nom: commune.nom,
      codePostal: commune.code_postal,
      latitude: parseFloat(commune.latitude),
      longitude: parseFloat(commune.longitude),
      departement: commune.departement
    }));
  } catch (error) {
    console.error('Erreur lors de la récupération des communes:', error);
    return [];
  }
}

export async function getPlansEau(): Promise<PlanEau[]> {
  try {
    const { data, error } = await supabase
      .from('plans_eau')
      .select('*')
      .eq('accessible', true)
      .eq('ecopage', true); // Filtrer seulement les plans d'eau avec écopage possible

    if (error) {
      console.error('Erreur lors de la récupération des plans d\'eau:', error);
      return [];
    }

    return data.map(planEau => ({
      id: planEau.id,
      nom: planEau.nom,
      latitude: parseFloat(planEau.latitude),
      longitude: parseFloat(planEau.longitude),
      accessible: planEau.accessible,
      commune: planEau.commune || undefined,
      ecopage: planEau.ecopage
    }));
  } catch (error) {
    console.error('Erreur lors de la récupération des plans d\'eau:', error);
    return [];
  }
}

export async function getBasesDash(): Promise<BaseDash[]> {
  try {
    const { data, error } = await supabase
      .from('bases_dash')
      .select('*')
      .eq('disponible', true);

    if (error) {
      console.error('Erreur lors de la récupération des pélicandromes:', error);
      return [];
    }

    return data.map(baseDash => ({
      id: baseDash.id,
      nom: baseDash.nom,
      latitude: parseFloat(baseDash.latitude),
      longitude: parseFloat(baseDash.longitude),
      capacite: baseDash.capacite,
      disponible: baseDash.disponible
    }));
  } catch (error) {
    console.error('Erreur lors de la récupération des pélicandromes:', error);
    return [];
  }
}

export async function searchCommunes(searchTerm: string): Promise<Commune[]> {
  try {
    const { data, error } = await supabase
      .from('communes')
      .select('*')
      .or(`nom.ilike.%${searchTerm}%,code_postal.ilike.%${searchTerm}%`)
      .order('nom')
      .limit(10);

    if (error) {
      console.error('Erreur lors de la recherche de communes:', error);
      return [];
    }

    return data.map(commune => ({
      id: commune.id,
      nom: commune.nom,
      codePostal: commune.code_postal,
      latitude: parseFloat(commune.latitude),
      longitude: parseFloat(commune.longitude),
      departement: commune.departement
    }));
  } catch (error) {
    console.error('Erreur lors de la recherche de communes:', error);
    return [];
  }
}

/**
 * Récupère tous les plans d'eau (avec et sans écopage) pour l'administration
 */
export async function getAllPlansEau(): Promise<PlanEau[]> {
  try {
    const { data, error } = await supabase
      .from('plans_eau')
      .select('*')
      .order('commune', { ascending: true });

    if (error) {
      console.error('Erreur lors de la récupération de tous les plans d\'eau:', error);
      return [];
    }

    return data.map(planEau => ({
      id: planEau.id,
      nom: planEau.nom,
      latitude: parseFloat(planEau.latitude),
      longitude: parseFloat(planEau.longitude),
      accessible: planEau.accessible,
      commune: planEau.commune || undefined,
      ecopage: planEau.ecopage
    }));
  } catch (error) {
    console.error('Erreur lors de la récupération de tous les plans d\'eau:', error);
    return [];
  }
}

/**
 * Ajoute un nouveau plan d'eau
 */
export async function addPlanEau(planEau: {
  nom: string;
  latitude: number;
  longitude: number;
  commune?: string;
  accessible?: boolean;
  ecopage?: boolean;
}): Promise<PlanEau> {
  try {
    const { data, error } = await supabase
      .from('plans_eau')
      .insert({
        nom: planEau.nom,
        latitude: planEau.latitude,
        longitude: planEau.longitude,
        commune: planEau.commune || null,
        accessible: planEau.accessible ?? true,
        ecopage: planEau.ecopage ?? true
      })
      .select()
      .single();

    if (error) {
      throw error;
    }

    return {
      id: data.id,
      nom: data.nom,
      latitude: parseFloat(data.latitude),
      longitude: parseFloat(data.longitude),
      accessible: data.accessible,
      commune: data.commune || undefined,
      ecopage: data.ecopage
    };
  } catch (error) {
    console.error('Erreur lors de l\'ajout du plan d\'eau:', error);
    throw error;
  }
}

/**
 * Ajoute un nouveau pélicandrome
 */
export async function addBaseDash(baseDash: {
  nom: string;
  latitude: number;
  longitude: number;
  capacite: number;
  disponible?: boolean;
}): Promise<BaseDash> {
  try {
    const { data, error } = await supabase
      .from('bases_dash')
      .insert({
        nom: baseDash.nom,
        latitude: baseDash.latitude,
        longitude: baseDash.longitude,
        capacite: baseDash.capacite,
        disponible: baseDash.disponible ?? true
      })
      .select()
      .single();

    if (error) {
      throw error;
    }

    return {
      id: data.id,
      nom: data.nom,
      latitude: parseFloat(data.latitude),
      longitude: parseFloat(data.longitude),
      capacite: data.capacite,
      disponible: data.disponible
    };
  } catch (error) {
    console.error('Erreur lors de l\'ajout du pélicandrome:', error);
    throw error;
  }
}

/**
 * Supprime un plan d'eau
 */
export async function deletePlanEau(id: string): Promise<void> {
  try {
    const { error } = await supabase
      .from('plans_eau')
      .delete()
      .eq('id', id);

    if (error) {
      throw error;
    }
  } catch (error) {
    console.error('Erreur lors de la suppression du plan d\'eau:', error);
    throw error;
  }
}

/**
 * Supprime un pélicandrome
 */
export async function deleteBaseDash(id: string): Promise<void> {
  try {
    const { error } = await supabase
      .from('bases_dash')
      .delete()
      .eq('id', id);

    if (error) {
      throw error;
    }
  } catch (error) {
    console.error('Erreur lors de la suppression du pélicandrome:', error);
    throw error;
  }
}