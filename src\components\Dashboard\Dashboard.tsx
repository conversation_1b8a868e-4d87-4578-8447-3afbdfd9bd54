import React, { useState, useEffect } from 'react';
import { <PERSON>culator, AlertTriangle, Settings } from 'lucide-react';
import { Commune, CalculResult, InterventionParams, SimulatorSettings, SaveSimulationData } from '../../types';
import { CommuneSearch } from './CommuneSearch';
import { ResultsPanel } from './ResultsPanel';
import { InterventionMap } from './InterventionMap';
import { SaveSimulationModal } from './SaveSimulationModal';
import { calculerMoyensAeriens } from '../../services/calculService';
import { loadUserSettings } from '../../services/settingsService';
import { saveSimulation } from '../../services/simulationService';

// Paramètres par défaut si aucun paramètre utilisateur n'est trouvé
const defaultSettings: SimulatorSettings = {
  canadair: {
    vitesseCroisiere: 300,
    vitesseMaxLargage: 250,
    vitesseMinLargage: 180,
    capaciteReservoir: 6000,
    tempsRemplissage: 1,
    rayonBraquage: 800,
    altitudeMaxLargage: 150,
    altitudeMinLargage: 30,
    consommationCarburant: 1200,
    autonomieVol: 4.5
  },
  dash: {
    vitesseCroisiere: 400,
    vitesseMaxLargage: 320,
    vitesseMinLargage: 220,
    capaciteReservoir: 3000,
    tempsRemplissage: 3,
    rayonBraquage: 600,
    altitudeMaxLargage: 200,
    altitudeMinLargage: 50,
    consommationCarburant: 800,
    autonomieVol: 6
  },
  facteurVent: 1.0,
  facteurVisibilite: 1.0,
  tempsPreparationMission: 15,
  tempsRetourBase: 10,
  margeSecurite: 20,
  // Nouveaux paramètres opérationnels
  exigencesCanadair: 20,
  exigencesDash: 5,
  dureeMission: 60
};

export function Dashboard() {
  const [selectedCommune, setSelectedCommune] = useState<Commune>();
  const [results, setResults] = useState<CalculResult>();
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState<string | null>(null);
  const [settings, setSettings] = useState<SimulatorSettings>(defaultSettings);
  const [settingsLoading, setSettingsLoading] = useState(true);
  const [showSaveModal, setShowSaveModal] = useState(false);
  const [saveLoading, setSaveLoading] = useState(false);

  // Charger les paramètres utilisateur au démarrage
  useEffect(() => {
    const loadSettings = async () => {
      try {
        setSettingsLoading(true);
        const userSettings = await loadUserSettings();
        if (userSettings) {
          // Fusionner avec les paramètres par défaut pour gérer les nouveaux paramètres
          setSettings({
            ...defaultSettings,
            ...userSettings,
            // S'assurer que les nouveaux paramètres ont des valeurs par défaut
            exigencesCanadair: userSettings.exigencesCanadair ?? defaultSettings.exigencesCanadair,
            exigencesDash: userSettings.exigencesDash ?? defaultSettings.exigencesDash,
            dureeMission: userSettings.dureeMission ?? defaultSettings.dureeMission
          });
        }
      } catch (error) {
        console.error('Erreur lors du chargement des paramètres:', error);
        // Utiliser les paramètres par défaut en cas d'erreur
      } finally {
        setSettingsLoading(false);
      }
    };

    loadSettings();
  }, []);

  const handleCommuneSelect = async (commune: Commune) => {
    setSelectedCommune(commune);
    setLoading(true);
    setError(null);

    try {
      // Paramètres selon les paramètres utilisateur
      const params: InterventionParams = {
        communeId: commune.id,
        typeIntervention: 'incendie_foret', // Valeur fixe
        exigencesCanadair: settings.exigencesCanadair,
        exigencesDash: settings.exigencesDash
      };

      // Calculer avec les paramètres utilisateur
      const calculResults = await calculerMoyensAeriens(commune, params, settings);
      setResults(calculResults);
    } catch (err) {
      console.error('Erreur lors du calcul:', err);
      setError('Erreur lors du calcul des moyens aériens. Vérifiez que les données sont bien configurées dans Supabase.');
    } finally {
      setLoading(false);
    }
  };

  const handleSaveSimulation = async (data: { nom: string; description?: string }) => {
    if (!selectedCommune || !results) return;

    setSaveLoading(true);
    try {
      const params: InterventionParams = {
        communeId: selectedCommune.id,
        typeIntervention: 'incendie_foret',
        exigencesCanadair: settings.exigencesCanadair,
        exigencesDash: settings.exigencesDash
      };

      const simulationData: SaveSimulationData = {
        nom: data.nom,
        description: data.description,
        commune_data: selectedCommune,
        intervention_params: params,
        simulator_settings: settings,
        results: results
      };

      await saveSimulation(simulationData);
      // Optionnel: afficher un message de succès
    } catch (err) {
      console.error('Erreur lors de la sauvegarde:', err);
      throw err; // Le modal gérera l'erreur
    } finally {
      setSaveLoading(false);
    }
  };

  // Recalculer si les paramètres changent et qu'une commune est sélectionnée
  useEffect(() => {
    if (selectedCommune && !settingsLoading) {
      handleCommuneSelect(selectedCommune);
    }
  }, [settings, settingsLoading]);

  if (settingsLoading) {
    return (
      <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8">
        <div className="bg-white rounded-lg shadow-md p-12 text-center">
          <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-red-600 mx-auto mb-4"></div>
          <p className="text-gray-600">Chargement des paramètres...</p>
        </div>
      </div>
    );
  }

  return (
    <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8">
      <div className="mb-8">
        <h2 className="text-2xl font-bold text-gray-900 mb-2">Estimation des moyens aériens</h2>
        <p className="text-gray-600">
          Sélectionnez une commune pour calculer automatiquement les besoins en Canadair et Dash
        </p>
      </div>

      <div className="grid grid-cols-1 lg:grid-cols-3 gap-8">
        {/* Panneau de saisie */}
        <div className="lg:col-span-1">
          <div className="bg-white rounded-lg shadow-md p-6 space-y-6">
            <h3 className="text-lg font-semibold text-gray-800 flex items-center space-x-2">
              <Calculator className="h-5 w-5 text-red-600" />
              <span>Paramètres d'intervention</span>
            </h3>

            <CommuneSearch 
              onCommuneSelect={handleCommuneSelect}
              selectedCommune={selectedCommune}
            />

            <div className="bg-blue-50 border border-blue-200 rounded-lg p-4">
              <h4 className="font-medium text-blue-800 mb-2 flex items-center space-x-2">
                <Settings className="h-4 w-4" />
                <span>Paramètres actifs</span>
              </h4>
              <div className="text-sm text-blue-700 space-y-1">
                <div>• Vitesse Canadair: {settings.canadair.vitesseCroisiere} km/h</div>
                <div>• Forfait écopage canadair: {settings.canadair.tempsRemplissage} min</div>
                <div>• Vitesse Dash: {settings.dash.vitesseCroisiere} km/h</div>
                <div>• Temps remplissage Dash: {settings.dash.tempsRemplissage} min</div>
                <div>• Facteur vent: {settings.facteurVent}</div>
                <div>• Marge sécurité: {settings.margeSecurite}%</div>
              </div>
            </div>

            <div className="bg-yellow-50 border border-yellow-200 rounded-lg p-4">
              <h4 className="font-medium text-yellow-800 mb-2 flex items-center space-x-2">
                <AlertTriangle className="h-4 w-4" />
                <span>Objectifs opérationnels</span>
              </h4>
              <div className="text-sm text-yellow-700 space-y-1">
                <div>• Exigence Canadair: {settings.exigencesCanadair} largages par heure</div>
                <div>• Exigence Dash: {settings.exigencesDash} largages par heure</div>
              </div>
            </div>

            {error && (
              <div className="bg-red-50 border border-red-200 rounded-lg p-4">
                <h4 className="font-medium text-red-800 mb-2">Erreur</h4>
                <p className="text-sm text-red-700">{error}</p>
              </div>
            )}
          </div>
        </div>

        {/* Résultats */}
        <div className="lg:col-span-2">
          {loading ? (
            <div className="bg-white rounded-lg shadow-md p-12 text-center">
              <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-red-600 mx-auto mb-4"></div>
              <p className="text-gray-600">Calcul en cours...</p>
            </div>
          ) : results ? (
            <ResultsPanel
              results={results}
              onSaveSimulation={() => setShowSaveModal(true)}
            />
          ) : (
            <div className="bg-gray-50 border-2 border-dashed border-gray-300 rounded-lg p-12 text-center">
              <Calculator className="mx-auto h-12 w-12 text-gray-400 mb-4" />
              <h3 className="text-lg font-medium text-gray-900 mb-2">
                Aucune commune sélectionnée
              </h3>
              <p className="text-gray-600">
                Sélectionnez une commune pour voir les calculs et la visualisation cartographique
              </p>
            </div>
          )}
        </div>
      </div>

      {/* Carte en pleine largeur */}
      {results && (
        <div className="mt-8 -mx-4 sm:-mx-6 lg:-mx-8">
          <div className="px-4 sm:px-6 lg:px-8">
            <InterventionMap results={results} height={600} />
          </div>
        </div>
      )}

      {/* Modal de sauvegarde */}
      {showSaveModal && selectedCommune && results && (
        <SaveSimulationModal
          isOpen={showSaveModal}
          onClose={() => setShowSaveModal(false)}
          onSave={handleSaveSimulation}
          simulationData={{
            nom: '',
            commune_data: selectedCommune,
            intervention_params: {
              communeId: selectedCommune.id,
              typeIntervention: 'incendie_foret',
              exigencesCanadair: settings.exigencesCanadair,
              exigencesDash: settings.exigencesDash
            },
            simulator_settings: settings,
            results: results
          }}
          loading={saveLoading}
        />
      )}
    </div>
  );
}