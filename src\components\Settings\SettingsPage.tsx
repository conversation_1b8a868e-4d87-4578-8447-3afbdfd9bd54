import React, { useState, useEffect } from 'react';
import { 
  Settings, 
  Plane, 
  Save, 
  RotateCcw, 
  Download, 
  Upload,
  Info,
  Gauge,
  Timer,
  Fuel,
  Wind,
  Eye,
  Shield,
  Target,
  Database,
  ArrowLeft
} from 'lucide-react';
import { SimulatorSettings, AircraftSettings } from '../../types';
import { ParameterSlider } from './ParameterSlider';
import { ParameterInput } from './ParameterInput';
import { Tooltip } from './Tooltip';
import { DataManagementForm } from './DataManagementForm';
import { Header } from '../Layout/Header';
import { saveUserSettings, loadUserSettings } from '../../services/settingsService';
import { useAuth } from '../../hooks/useAuth';

// Fonction pour convertir km/h en nœuds
function convertKmhToKnots(kmh: number): number {
  // 1 nœud = 1,852 km/h
  return kmh / 1.852;
}

function formatSpeedUnit(kmh: number): string {
  const knots = convertKmhToKnots(kmh);
  return `kts (${kmh} km/h)`;
}

const defaultSettings: SimulatorSettings = {
  canadair: {
    vitesseCroisiere: 300,
    vitesseMaxLargage: 250,
    vitesseMinLargage: 180,
    capaciteReservoir: 6000,
    tempsRemplissage: 1.5,
    rayonBraquage: 800,
    altitudeMaxLargage: 150,
    altitudeMinLargage: 30,
    consommationCarburant: 1200,
    autonomieVol: 4.5
  },
  dash: {
    vitesseCroisiere: 400,
    vitesseMaxLargage: 320,
    vitesseMinLargage: 220,
    capaciteReservoir: 3000,
    tempsRemplissage: 3,
    rayonBraquage: 600,
    altitudeMaxLargage: 200,
    altitudeMinLargage: 50,
    consommationCarburant: 800,
    autonomieVol: 6
  },
  facteurVent: 1.0,
  facteurVisibilite: 1.0,
  tempsPreparationMission: 15,
  tempsRetourBase: 10,
  margeSecurite: 20,
  // Nouveaux paramètres opérationnels
  exigencesCanadair: 20,
  exigencesDash: 5,
  dureeMission: 60
};

interface SettingsPageProps {
  onBack: () => void;
}

export function SettingsPage({ onBack }: SettingsPageProps) {
  const { user, logout } = useAuth();
  const [settings, setSettings] = useState<SimulatorSettings>(defaultSettings);
  const [hasChanges, setHasChanges] = useState(false);
  const [activeTab, setActiveTab] = useState<'canadair' | 'dash' | 'environment' | 'operational' | 'data'>('canadair');
  const [saving, setSaving] = useState(false);
  const [loading, setLoading] = useState(true);

  useEffect(() => {
    const loadSettings = async () => {
      if (!user) return;
      
      try {
        setLoading(true);
        const userSettings = await loadUserSettings();
        if (userSettings) {
          // Fusionner avec les paramètres par défaut pour gérer les nouveaux paramètres
          setSettings({
            ...defaultSettings,
            ...userSettings,
            // S'assurer que les nouveaux paramètres ont des valeurs par défaut
            exigencesCanadair: userSettings.exigencesCanadair ?? defaultSettings.exigencesCanadair,
            exigencesDash: userSettings.exigencesDash ?? defaultSettings.exigencesDash,
            dureeMission: userSettings.dureeMission ?? defaultSettings.dureeMission
          });
        }
      } catch (error) {
        console.error('Erreur lors du chargement des paramètres:', error);
      } finally {
        setLoading(false);
      }
    };

    loadSettings();
  }, [user]);

  const updateAircraftSetting = (aircraft: 'canadair' | 'dash', key: keyof AircraftSettings, value: number) => {
    setSettings(prev => ({
      ...prev,
      [aircraft]: {
        ...prev[aircraft],
        [key]: value
      }
    }));
    setHasChanges(true);
  };

  const updateEnvironmentSetting = (key: keyof Omit<SimulatorSettings, 'canadair' | 'dash'>, value: number) => {
    setSettings(prev => ({
      ...prev,
      [key]: value
    }));
    setHasChanges(true);
  };

  const saveSettings = async () => {
    if (!user) {
      throw new Error('Aucun utilisateur connecté');
    }

    try {
      setSaving(true);
      await saveUserSettings(settings);
      setHasChanges(false);
      
      // Afficher un message de succès temporaire
      const successMessage = document.createElement('div');
      successMessage.className = 'fixed top-4 right-4 bg-green-100 border border-green-300 text-green-800 px-4 py-2 rounded-lg shadow-lg z-50';
      successMessage.innerHTML = '✅ Paramètres sauvegardés avec succès !';
      document.body.appendChild(successMessage);
      
      setTimeout(() => {
        document.body.removeChild(successMessage);
      }, 3000);
      
    } catch (error) {
      console.error('Erreur lors de la sauvegarde:', error);
      
      // Afficher un message d'erreur
      const errorMessage = document.createElement('div');
      errorMessage.className = 'fixed top-4 right-4 bg-red-100 border border-red-300 text-red-800 px-4 py-2 rounded-lg shadow-lg z-50';
      errorMessage.innerHTML = '❌ Erreur lors de la sauvegarde';
      document.body.appendChild(errorMessage);
      
      setTimeout(() => {
        document.body.removeChild(errorMessage);
      }, 3000);
    } finally {
      setSaving(false);
    }
  };

  const showMessage = (message: string, type: 'success' | 'error') => {
    const messageDiv = document.createElement('div');
    messageDiv.className = `fixed top-4 right-4 px-4 py-2 rounded-lg shadow-lg z-50 ${
      type === 'success' 
        ? 'bg-green-100 border border-green-300 text-green-800' 
        : 'bg-red-100 border border-red-300 text-red-800'
    }`;
    messageDiv.innerHTML = `${type === 'success' ? '✅' : '❌'} ${message}`;
    document.body.appendChild(messageDiv);
    
    setTimeout(() => {
      document.body.removeChild(messageDiv);
    }, 3000);
  };

  const renderAircraftSettings = (aircraft: 'canadair' | 'dash') => {
    const aircraftSettings = settings[aircraft];
    const aircraftName = aircraft === 'canadair' ? 'Canadair' : 'Dash';

    return (
      <div className="space-y-8">
        {/* Vitesses */}
        <div className="bg-white rounded-lg shadow-md p-6">
          <h3 className="text-lg font-semibold text-gray-800 mb-4 flex items-center space-x-2">
            <Gauge className="h-5 w-5 text-blue-600" />
            <span>Paramètres de vitesse</span>
          </h3>
          
          <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
            <ParameterSlider
              label="Vitesse de croisière"
              value={aircraftSettings.vitesseCroisiere}
              min={200}
              max={500}
              step={10}
              unit={formatSpeedUnit(aircraftSettings.vitesseCroisiere)}
              onChange={(value) => updateAircraftSetting(aircraft, 'vitesseCroisiere', value)}
              tooltip="Vitesse optimale pour les déplacements longue distance"
            />
            
            <ParameterSlider
              label="Vitesse max. largage"
              value={aircraftSettings.vitesseMaxLargage}
              min={150}
              max={400}
              step={5}
              unit={formatSpeedUnit(aircraftSettings.vitesseMaxLargage)}
              onChange={(value) => updateAircraftSetting(aircraft, 'vitesseMaxLargage', value)}
              tooltip="Vitesse maximale autorisée lors du largage"
            />
            
            <ParameterSlider
              label="Vitesse min. largage"
              value={aircraftSettings.vitesseMinLargage}
              min={100}
              max={300}
              step={5}
              unit={formatSpeedUnit(aircraftSettings.vitesseMinLargage)}
              onChange={(value) => updateAircraftSetting(aircraft, 'vitesseMinLargage', value)}
              tooltip="Vitesse minimale pour un largage efficace"
            />
          </div>
        </div>

        {/* Capacités */}
        <div className="bg-white rounded-lg shadow-md p-6">
          <h3 className="text-lg font-semibold text-gray-800 mb-4 flex items-center space-x-2">
            <Fuel className="h-5 w-5 text-green-600" />
            <span>Capacités et ravitaillement</span>
          </h3>
          
          <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
            <ParameterInput
              label="Capacité réservoir"
              value={aircraftSettings.capaciteReservoir}
              min={1000}
              max={10000}
              step={100}
              unit="litres"
              onChange={(value) => updateAircraftSetting(aircraft, 'capaciteReservoir', value)}
              tooltip="Volume maximum d'eau/retardant transportable"
            />
            
            <ParameterSlider
              label="Temps de remplissage"
              value={aircraftSettings.tempsRemplissage}
              min={0.5}
              max={20}
              step={0.1}
              unit="min"
              onChange={(value) => updateAircraftSetting(aircraft, 'tempsRemplissage', value)}
              tooltip="Durée nécessaire pour remplir complètement le réservoir"
            />
            
            <ParameterInput
              label="Consommation carburant"
              value={aircraftSettings.consommationCarburant}
              min={500}
              max={2000}
              step={50}
              unit="L/h"
              onChange={(value) => updateAircraftSetting(aircraft, 'consommationCarburant', value)}
              tooltip="Consommation de carburant en vol de croisière"
            />
            
            <ParameterSlider
              label="Autonomie de vol"
              value={aircraftSettings.autonomieVol}
              min={2}
              max={8}
              step={0.1}
              unit="heures"
              onChange={(value) => updateAircraftSetting(aircraft, 'autonomieVol', value)}
              tooltip="Durée maximale de vol sans ravitaillement"
            />
          </div>
        </div>

        {/* Manœuvrabilité */}
        <div className="bg-white rounded-lg shadow-md p-6">
          <h3 className="text-lg font-semibold text-gray-800 mb-4 flex items-center space-x-2">
            <Plane className="h-5 w-5 text-purple-600" />
            <span>Manœuvrabilité et altitudes</span>
          </h3>
          
          <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
            <ParameterInput
              label="Rayon de braquage"
              value={aircraftSettings.rayonBraquage}
              min={300}
              max={1500}
              step={50}
              unit="mètres"
              onChange={(value) => updateAircraftSetting(aircraft, 'rayonBraquage', value)}
              tooltip="Rayon minimal de virage à vitesse de largage"
            />
            
            <ParameterSlider
              label="Altitude max. largage"
              value={aircraftSettings.altitudeMaxLargage}
              min={50}
              max={300}
              step={10}
              unit="mètres"
              onChange={(value) => updateAircraftSetting(aircraft, 'altitudeMaxLargage', value)}
              tooltip="Altitude maximale pour un largage efficace"
            />
            
            <ParameterSlider
              label="Altitude min. largage"
              value={aircraftSettings.altitudeMinLargage}
              min={10}
              max={100}
              step={5}
              unit="mètres"
              onChange={(value) => updateAircraftSetting(aircraft, 'altitudeMinLargage', value)}
              tooltip="Altitude minimale de sécurité pour le largage"
            />
          </div>
        </div>
      </div>
    );
  };

  const renderEnvironmentSettings = () => (
    <div className="space-y-8">
      {/* Conditions environnementales */}
      <div className="bg-white rounded-lg shadow-md p-6">
        <h3 className="text-lg font-semibold text-gray-800 mb-4 flex items-center space-x-2">
          <Wind className="h-5 w-5 text-blue-600" />
          <span>Conditions environnementales</span>
        </h3>
        
        <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
          <ParameterSlider
            label="Facteur vent"
            value={settings.facteurVent}
            min={0.5}
            max={2.0}
            step={0.1}
            unit="coeff."
            onChange={(value) => updateEnvironmentSetting('facteurVent', value)}
            tooltip="Impact du vent sur les performances (1.0 = conditions normales)"
          />
          
          <ParameterSlider
            label="Facteur visibilité"
            value={settings.facteurVisibilite}
            min={0.5}
            max={1.0}
            step={0.05}
            unit="coeff."
            onChange={(value) => updateEnvironmentSetting('facteurVisibilite', value)}
            tooltip="Impact de la visibilité sur l'efficacité (1.0 = visibilité parfaite)"
          />
        </div>
      </div>

      {/* Paramètres opérationnels */}
      <div className="bg-white rounded-lg shadow-md p-6">
        <h3 className="text-lg font-semibold text-gray-800 mb-4 flex items-center space-x-2">
          <Timer className="h-5 w-5 text-green-600" />
          <span>Paramètres opérationnels</span>
        </h3>
        
        <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
          <ParameterSlider
            label="Temps préparation mission"
            value={settings.tempsPreparationMission}
            min={5}
            max={60}
            step={1}
            unit="min"
            onChange={(value) => updateEnvironmentSetting('tempsPreparationMission', value)}
            tooltip="Durée de préparation avant décollage"
          />
          
          <ParameterSlider
            label="Temps retour base"
            value={settings.tempsRetourBase}
            min={5}
            max={30}
            step={1}
            unit="min"
            onChange={(value) => updateEnvironmentSetting('tempsRetourBase', value)}
            tooltip="Temps additionnel pour le retour et l'atterrissage"
          />
          
          <ParameterSlider
            label="Marge de sécurité"
            value={settings.margeSecurite}
            min={10}
            max={50}
            step={1}
            unit="%"
            onChange={(value) => updateEnvironmentSetting('margeSecurite', value)}
            tooltip="Marge de sécurité appliquée aux calculs"
          />
        </div>
      </div>
    </div>
  );

  const renderOperationalSettings = () => (
    <div className="space-y-8">
      {/* Objectifs opérationnels */}
      <div className="bg-white rounded-lg shadow-md p-6">
        <h3 className="text-lg font-semibold text-gray-800 mb-4 flex items-center space-x-2">
          <Target className="h-5 w-5 text-red-600" />
          <span>Objectifs opérationnels</span>
        </h3>
        
        <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
          <ParameterSlider
            label="Exigence Canadair"
            value={settings.exigencesCanadair}
            min={1}
            max={40}
            step={1}
            unit="largages"
            onChange={(value) => updateEnvironmentSetting('exigencesCanadair', value)}
            tooltip="Nombre de largages Canadair requis pour l'intervention"
          />

          <ParameterSlider
            label="Exigence Dash"
            value={settings.exigencesDash}
            min={1}
            max={10}
            step={1}
            unit="largages"
            onChange={(value) => updateEnvironmentSetting('exigencesDash', value)}
            tooltip="Nombre de largages Dash requis pour l'intervention"
          />
          
          <ParameterSlider
            label="Durée de mission"
            value={settings.dureeMission}
            min={30}
            max={180}
            step={5}
            unit="min"
            onChange={(value) => updateEnvironmentSetting('dureeMission', value)}
            tooltip="Durée totale de la mission d'intervention"
          />
        </div>
      </div>

      {/* Informations sur les objectifs */}
      <div className="bg-blue-50 border border-blue-200 rounded-lg p-6">
        <h4 className="font-medium text-blue-800 mb-3 flex items-center space-x-2">
          <Info className="h-4 w-4" />
          <span>Impact des objectifs opérationnels</span>
        </h4>
        <div className="text-sm text-blue-700 space-y-2">
          <p>• <strong>Exigences de largage :</strong> Définissent le nombre total de largages nécessaires pour maîtriser l'incendie</p>
          <p>• <strong>Durée de mission :</strong> Influence le nombre de cycles que chaque appareil peut effectuer</p>
          <p>• <strong>Calcul automatique :</strong> Le nombre d'appareils nécessaires est calculé en fonction de ces paramètres</p>
        </div>
      </div>
    </div>
  );

  const renderDataManagement = () => (
    <div className="space-y-8">
      <div className="bg-white rounded-lg shadow-md p-6">
        <h3 className="text-lg font-semibold text-gray-800 mb-4 flex items-center space-x-2">
          <Database className="h-5 w-5 text-purple-600" />
          <span>Gestion des données</span>
        </h3>
        
        <div className="mb-6">
          <p className="text-gray-600 text-sm">
            Ajoutez de nouveaux plans d'eau ou pélicandromes à la base de données.
            Ces données seront immédiatement disponibles pour tous les calculs.
          </p>
        </div>

        <DataManagementForm
          onSuccess={(message) => showMessage(message, 'success')}
          onError={(message) => showMessage(message, 'error')}
        />
      </div>

      {/* Informations importantes */}
      <div className="bg-yellow-50 border border-yellow-200 rounded-lg p-6">
        <h4 className="font-medium text-yellow-800 mb-3 flex items-center space-x-2">
          <Info className="h-4 w-4" />
          <span>Informations importantes</span>
        </h4>
        <div className="text-sm text-yellow-700 space-y-2">
          <p>• <strong>Coordonnées GPS :</strong> Utilisez le format décimal (ex: 43.7567, 4.4164)</p>
          <p>• <strong>Validation :</strong> Les coordonnées sont automatiquement validées (lat: -90 à 90, lon: -180 à 180)</p>
          <p>• <strong>Écopage :</strong> Seuls les plans d'eau avec écopage possible sont utilisés pour les Canadair</p>
          <p>• <strong>Disponibilité :</strong> Seules les bases disponibles sont prises en compte dans les calculs</p>
        </div>
      </div>
    </div>
  );

  if (loading) {
    return (
      <div className="min-h-screen bg-gray-100">
        <Header user={user} onLogout={logout} />
        <div className="flex items-center justify-center py-20">
          <div className="text-center">
            <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-red-600 mx-auto mb-4"></div>
            <p className="text-gray-600">Chargement des paramètres...</p>
          </div>
        </div>
      </div>
    );
  }

  return (
    <div className="min-h-screen bg-gray-100">
      {/* Header FireSplash */}
      <Header user={user} onLogout={logout} />
      
      {/* Sub-header pour les paramètres */}
      <div className="bg-white shadow-sm border-b">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <div className="flex justify-between items-center h-16">
            <div className="flex items-center space-x-3">
              <button
                onClick={onBack}
                className="flex items-center space-x-2 text-gray-600 hover:text-gray-900 transition-colors"
              >
                <ArrowLeft className="h-5 w-5" />
                <span>Retour au tableau de bord</span>
              </button>
              <div className="h-6 w-px bg-gray-300"></div>
              <Settings className="h-6 w-6 text-red-600" />
              <h1 className="text-xl font-bold text-gray-900">Paramètres du simulateur</h1>
            </div>
            
            <div className="flex items-center space-x-3">
              <button
                onClick={saveSettings}
                disabled={!hasChanges || saving}
                className="flex items-center space-x-1 px-4 py-2 text-sm bg-red-600 hover:bg-red-700 text-white rounded-md transition-colors disabled:opacity-50 disabled:cursor-not-allowed"
              >
                <Save className="h-4 w-4" />
                <span>{saving ? 'Sauvegarde...' : 'Sauvegarder'}</span>
              </button>
            </div>
          </div>
        </div>
      </div>

      <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8">
        {/* Tabs */}
        <div className="mb-8">
          <div className="border-b border-gray-200">
            <nav className="-mb-px flex space-x-8">
              <button
                onClick={() => setActiveTab('canadair')}
                className={`py-2 px-1 border-b-2 font-medium text-sm transition-colors ${
                  activeTab === 'canadair'
                    ? 'border-red-500 text-red-600'
                    : 'border-transparent text-gray-500 hover:text-gray-700 hover:border-gray-300'
                }`}
              >
                <div className="flex items-center space-x-2">
                  <Plane className="h-4 w-4" />
                  <span>Canadair</span>
                </div>
              </button>
              
              <button
                onClick={() => setActiveTab('dash')}
                className={`py-2 px-1 border-b-2 font-medium text-sm transition-colors ${
                  activeTab === 'dash'
                    ? 'border-red-500 text-red-600'
                    : 'border-transparent text-gray-500 hover:text-gray-700 hover:border-gray-300'
                }`}
              >
                <div className="flex items-center space-x-2">
                  <Plane className="h-4 w-4" />
                  <span>Dash</span>
                </div>
              </button>
              
              <button
                onClick={() => setActiveTab('environment')}
                className={`py-2 px-1 border-b-2 font-medium text-sm transition-colors ${
                  activeTab === 'environment'
                    ? 'border-red-500 text-red-600'
                    : 'border-transparent text-gray-500 hover:text-gray-700 hover:border-gray-300'
                }`}
              >
                <div className="flex items-center space-x-2">
                  <Shield className="h-4 w-4" />
                  <span>Environnement</span>
                </div>
              </button>
              
              <button
                onClick={() => setActiveTab('operational')}
                className={`py-2 px-1 border-b-2 font-medium text-sm transition-colors ${
                  activeTab === 'operational'
                    ? 'border-red-500 text-red-600'
                    : 'border-transparent text-gray-500 hover:text-gray-700 hover:border-gray-300'
                }`}
              >
                <div className="flex items-center space-x-2">
                  <Target className="h-4 w-4" />
                  <span>Objectifs</span>
                </div>
              </button>

              <button
                onClick={() => setActiveTab('data')}
                className={`py-2 px-1 border-b-2 font-medium text-sm transition-colors ${
                  activeTab === 'data'
                    ? 'border-red-500 text-red-600'
                    : 'border-transparent text-gray-500 hover:text-gray-700 hover:border-gray-300'
                }`}
              >
                <div className="flex items-center space-x-2">
                  <Database className="h-4 w-4" />
                  <span>Données</span>
                </div>
              </button>
            </nav>
          </div>
        </div>

        {/* Content */}
        {activeTab === 'canadair' && renderAircraftSettings('canadair')}
        {activeTab === 'dash' && renderAircraftSettings('dash')}
        {activeTab === 'environment' && renderEnvironmentSettings()}
        {activeTab === 'operational' && renderOperationalSettings()}
        {activeTab === 'data' && renderDataManagement()}

        {/* Status */}
        {hasChanges && (
          <div className="fixed bottom-4 right-4 bg-yellow-100 border border-yellow-300 text-yellow-800 px-4 py-2 rounded-lg shadow-lg">
            <div className="flex items-center space-x-2">
              <Info className="h-4 w-4" />
              <span className="text-sm">Modifications non sauvegardées</span>
            </div>
          </div>
        )}
      </div>
    </div>
  );
}