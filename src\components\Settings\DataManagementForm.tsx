import React, { useState } from 'react';
import { Plus, Droplet, Plane, Save, X } from 'lucide-react';
import { addPlanEau, addBaseDash } from '../../services/supabaseService';

interface DataManagementFormProps {
  onSuccess: (message: string) => void;
  onError: (message: string) => void;
}

export function DataManagementForm({ onSuccess, onError }: DataManagementFormProps) {
  const [activeForm, setActiveForm] = useState<'none' | 'planeau' | 'basedash'>('none');
  const [loading, setLoading] = useState(false);

  // États pour le formulaire plan d'eau
  const [planEauForm, setPlanEauForm] = useState({
    nom: '',
    latitude: '',
    longitude: '',
    commune: '',
    accessible: true,
    ecopage: true
  });

  // États pour le formulaire pélicandrome
  const [baseDashForm, setBaseDashForm] = useState({
    nom: '',
    latitude: '',
    longitude: '',
    capacite: '',
    disponible: true
  });

  const resetPlanEauForm = () => {
    setPlanEauForm({
      nom: '',
      latitude: '',
      longitude: '',
      commune: '',
      accessible: true,
      ecopage: true
    });
  };

  const resetBaseDashForm = () => {
    setBaseDashForm({
      nom: '',
      latitude: '',
      longitude: '',
      capacite: '',
      disponible: true
    });
  };

  const validateCoordinates = (lat: number, lon: number): boolean => {
    return lat >= -90 && lat <= 90 && lon >= -180 && lon <= 180;
  };

  const handlePlanEauSubmit = async (e: React.FormEvent) => {
    e.preventDefault();
    
    if (!planEauForm.nom.trim()) {
      onError('Le nom du plan d\'eau est obligatoire');
      return;
    }

    const latitude = parseFloat(planEauForm.latitude);
    const longitude = parseFloat(planEauForm.longitude);

    if (isNaN(latitude) || isNaN(longitude)) {
      onError('Les coordonnées doivent être des nombres valides');
      return;
    }

    if (!validateCoordinates(latitude, longitude)) {
      onError('Les coordonnées GPS ne sont pas valides (lat: -90 à 90, lon: -180 à 180)');
      return;
    }

    try {
      setLoading(true);
      await addPlanEau({
        nom: planEauForm.nom.trim(),
        latitude,
        longitude,
        commune: planEauForm.commune.trim() || undefined,
        accessible: planEauForm.accessible,
        ecopage: planEauForm.ecopage
      });

      onSuccess(`Plan d'eau "${planEauForm.nom}" ajouté avec succès`);
      resetPlanEauForm();
      setActiveForm('none');
    } catch (error: any) {
      onError(`Erreur lors de l'ajout du plan d'eau: ${error.message}`);
    } finally {
      setLoading(false);
    }
  };

  const handleBaseDashSubmit = async (e: React.FormEvent) => {
    e.preventDefault();
    
    if (!baseDashForm.nom.trim()) {
      onError('Le nom du pélicandrome est obligatoire');
      return;
    }

    const latitude = parseFloat(baseDashForm.latitude);
    const longitude = parseFloat(baseDashForm.longitude);
    const capacite = parseInt(baseDashForm.capacite);

    if (isNaN(latitude) || isNaN(longitude)) {
      onError('Les coordonnées doivent être des nombres valides');
      return;
    }

    if (!validateCoordinates(latitude, longitude)) {
      onError('Les coordonnées GPS ne sont pas valides (lat: -90 à 90, lon: -180 à 180)');
      return;
    }

    if (isNaN(capacite) || capacite < 1) {
      onError('La capacité doit être un nombre entier positif');
      return;
    }

    try {
      setLoading(true);
      await addBaseDash({
        nom: baseDashForm.nom.trim(),
        latitude,
        longitude,
        capacite,
        disponible: baseDashForm.disponible
      });

      onSuccess(`Pélicandrome "${baseDashForm.nom}" ajouté avec succès`);
      resetBaseDashForm();
      setActiveForm('none');
    } catch (error: any) {
      onError(`Erreur lors de l'ajout du pélicandrome: ${error.message}`);
    } finally {
      setLoading(false);
    }
  };

  return (
    <div className="space-y-6">
      {/* Boutons pour ouvrir les formulaires */}
      {activeForm === 'none' && (
        <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
          <button
            onClick={() => setActiveForm('planeau')}
            className="flex items-center justify-center space-x-2 p-4 bg-blue-50 hover:bg-blue-100 border border-blue-200 rounded-lg transition-colors"
          >
            <Droplet className="h-5 w-5 text-blue-600" />
            <span className="font-medium text-blue-800">Ajouter un plan d'eau</span>
            <Plus className="h-4 w-4 text-blue-600" />
          </button>

          <button
            onClick={() => setActiveForm('basedash')}
            className="flex items-center justify-center space-x-2 p-4 bg-green-50 hover:bg-green-100 border border-green-200 rounded-lg transition-colors"
          >
            <Plane className="h-5 w-5 text-green-600" />
            <span className="font-medium text-green-800">Ajouter un pélicandrome</span>
            <Plus className="h-4 w-4 text-green-600" />
          </button>
        </div>
      )}

      {/* Formulaire plan d'eau */}
      {activeForm === 'planeau' && (
        <div className="bg-white rounded-lg shadow-md p-6">
          <div className="flex items-center justify-between mb-4">
            <h3 className="text-lg font-semibold text-gray-800 flex items-center space-x-2">
              <Droplet className="h-5 w-5 text-blue-600" />
              <span>Nouveau plan d'eau</span>
            </h3>
            <button
              onClick={() => {
                setActiveForm('none');
                resetPlanEauForm();
              }}
              className="text-gray-400 hover:text-gray-600"
            >
              <X className="h-5 w-5" />
            </button>
          </div>

          <form onSubmit={handlePlanEauSubmit} className="space-y-4">
            <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
              <div>
                <label className="block text-sm font-medium text-gray-700 mb-2">
                  Nom du plan d'eau *
                </label>
                <input
                  type="text"
                  value={planEauForm.nom}
                  onChange={(e) => setPlanEauForm(prev => ({ ...prev, nom: e.target.value }))}
                  className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
                  placeholder="Ex: Lac de Sainte-Croix"
                  required
                />
              </div>

              <div>
                <label className="block text-sm font-medium text-gray-700 mb-2">
                  Commune
                </label>
                <input
                  type="text"
                  value={planEauForm.commune}
                  onChange={(e) => setPlanEauForm(prev => ({ ...prev, commune: e.target.value }))}
                  className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
                  placeholder="Ex: Sainte-Croix-du-Verdon"
                />
              </div>

              <div>
                <label className="block text-sm font-medium text-gray-700 mb-2">
                  Latitude *
                </label>
                <input
                  type="number"
                  step="any"
                  value={planEauForm.latitude}
                  onChange={(e) => setPlanEauForm(prev => ({ ...prev, latitude: e.target.value }))}
                  className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
                  placeholder="Ex: 43.7750"
                  required
                />
              </div>

              <div>
                <label className="block text-sm font-medium text-gray-700 mb-2">
                  Longitude *
                </label>
                <input
                  type="number"
                  step="any"
                  value={planEauForm.longitude}
                  onChange={(e) => setPlanEauForm(prev => ({ ...prev, longitude: e.target.value }))}
                  className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
                  placeholder="Ex: 6.2000"
                  required
                />
              </div>
            </div>

            <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
              <div className="flex items-center space-x-2">
                <input
                  type="checkbox"
                  id="accessible"
                  checked={planEauForm.accessible}
                  onChange={(e) => setPlanEauForm(prev => ({ ...prev, accessible: e.target.checked }))}
                  className="rounded border-gray-300 text-blue-600 focus:ring-blue-500"
                />
                <label htmlFor="accessible" className="text-sm font-medium text-gray-700">
                  Plan d'eau accessible
                </label>
              </div>

              <div className="flex items-center space-x-2">
                <input
                  type="checkbox"
                  id="ecopage"
                  checked={planEauForm.ecopage}
                  onChange={(e) => setPlanEauForm(prev => ({ ...prev, ecopage: e.target.checked }))}
                  className="rounded border-gray-300 text-blue-600 focus:ring-blue-500"
                />
                <label htmlFor="ecopage" className="text-sm font-medium text-gray-700">
                  Écopage possible
                </label>
              </div>
            </div>

            <div className="flex justify-end space-x-3 pt-4">
              <button
                type="button"
                onClick={() => {
                  setActiveForm('none');
                  resetPlanEauForm();
                }}
                className="px-4 py-2 text-sm font-medium text-gray-700 bg-gray-100 hover:bg-gray-200 rounded-md transition-colors"
              >
                Annuler
              </button>
              <button
                type="submit"
                disabled={loading}
                className="flex items-center space-x-2 px-4 py-2 text-sm font-medium text-white bg-blue-600 hover:bg-blue-700 rounded-md transition-colors disabled:opacity-50 disabled:cursor-not-allowed"
              >
                <Save className="h-4 w-4" />
                <span>{loading ? 'Ajout...' : 'Ajouter'}</span>
              </button>
            </div>
          </form>
        </div>
      )}

      {/* Formulaire pélicandrome */}
      {activeForm === 'basedash' && (
        <div className="bg-white rounded-lg shadow-md p-6">
          <div className="flex items-center justify-between mb-4">
            <h3 className="text-lg font-semibold text-gray-800 flex items-center space-x-2">
              <Plane className="h-5 w-5 text-green-600" />
              <span>Nouveau pélicandrome</span>
            </h3>
            <button
              onClick={() => {
                setActiveForm('none');
                resetBaseDashForm();
              }}
              className="text-gray-400 hover:text-gray-600"
            >
              <X className="h-5 w-5" />
            </button>
          </div>

          <form onSubmit={handleBaseDashSubmit} className="space-y-4">
            <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
              <div>
                <label className="block text-sm font-medium text-gray-700 mb-2">
                  Nom du pélicandrome *
                </label>
                <input
                  type="text"
                  value={baseDashForm.nom}
                  onChange={(e) => setBaseDashForm(prev => ({ ...prev, nom: e.target.value }))}
                  className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-green-500 focus:border-green-500"
                  placeholder="Ex: Base Nîmes-Garons"
                  required
                />
              </div>

              <div>
                <label className="block text-sm font-medium text-gray-700 mb-2">
                  Capacité *
                </label>
                <input
                  type="number"
                  min="1"
                  value={baseDashForm.capacite}
                  onChange={(e) => setBaseDashForm(prev => ({ ...prev, capacite: e.target.value }))}
                  className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-green-500 focus:border-green-500"
                  placeholder="Ex: 4"
                  required
                />
              </div>

              <div>
                <label className="block text-sm font-medium text-gray-700 mb-2">
                  Latitude *
                </label>
                <input
                  type="number"
                  step="any"
                  value={baseDashForm.latitude}
                  onChange={(e) => setBaseDashForm(prev => ({ ...prev, latitude: e.target.value }))}
                  className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-green-500 focus:border-green-500"
                  placeholder="Ex: 43.7567"
                  required
                />
              </div>

              <div>
                <label className="block text-sm font-medium text-gray-700 mb-2">
                  Longitude *
                </label>
                <input
                  type="number"
                  step="any"
                  value={baseDashForm.longitude}
                  onChange={(e) => setBaseDashForm(prev => ({ ...prev, longitude: e.target.value }))}
                  className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-green-500 focus:border-green-500"
                  placeholder="Ex: 4.4164"
                  required
                />
              </div>
            </div>

            <div className="flex items-center space-x-2">
              <input
                type="checkbox"
                id="disponible"
                checked={baseDashForm.disponible}
                onChange={(e) => setBaseDashForm(prev => ({ ...prev, disponible: e.target.checked }))}
                className="rounded border-gray-300 text-green-600 focus:ring-green-500"
              />
              <label htmlFor="disponible" className="text-sm font-medium text-gray-700">
                Pélicandrome disponible
              </label>
            </div>

            <div className="flex justify-end space-x-3 pt-4">
              <button
                type="button"
                onClick={() => {
                  setActiveForm('none');
                  resetBaseDashForm();
                }}
                className="px-4 py-2 text-sm font-medium text-gray-700 bg-gray-100 hover:bg-gray-200 rounded-md transition-colors"
              >
                Annuler
              </button>
              <button
                type="submit"
                disabled={loading}
                className="flex items-center space-x-2 px-4 py-2 text-sm font-medium text-white bg-green-600 hover:bg-green-700 rounded-md transition-colors disabled:opacity-50 disabled:cursor-not-allowed"
              >
                <Save className="h-4 w-4" />
                <span>{loading ? 'Ajout...' : 'Ajouter'}</span>
              </button>
            </div>
          </form>
        </div>
      )}
    </div>
  );
}