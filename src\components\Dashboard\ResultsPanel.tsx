import React from 'react';
import { Plane, Droplet, Timer, Target, Calculator, Save } from 'lucide-react';
import { CalculResult } from '../../types';

interface ResultsPanelProps {
  results: CalculResult;
  onSaveSimulation?: () => void;
}

function formatCalculResult(exactValue: number): string {
  // Si c'est un nombre entier, l'afficher tel quel
  if (Number.isInteger(exactValue)) {
    return exactValue.toString();
  }
  // Sinon, afficher l'arrondi supérieur
  return Math.ceil(exactValue).toString();
}

function formatExactValue(exactValue: number): string {
  // Afficher le calcul exact avec jusqu'à 3 décimales (sans zéros inutiles)
  if (Number.isInteger(exactValue)) {
    return exactValue.toString();
  }
  return parseFloat(exactValue.toFixed(3)).toString();
}

function convertKmToNauticMiles(km: number): number {
  // 1 nœud marin = 1,852 km
  return km / 1.852;
}

function formatDistance(km: number): string {
  const nauticMiles = convertKmToNauticMiles(km);
  return `${Math.round(nauticMiles * 100) / 100} nm (${km} km)`;
}

export function ResultsPanel({ results, onSaveSimulation }: ResultsPanelProps) {
  return (
    <div className="bg-white rounded-lg shadow-md p-6 space-y-6">
      <div className="flex items-center justify-between">
        <h3 className="text-lg font-semibold text-gray-800 flex items-center space-x-2">
          <Target className="h-5 w-5 text-red-600" />
          <span>Estimation des moyens nécessaires</span>
        </h3>
        {onSaveSimulation && (
          <button
            onClick={onSaveSimulation}
            className="flex items-center space-x-2 px-4 py-2 bg-red-600 text-white rounded-lg hover:bg-red-700 transition-colors"
          >
            <Save className="h-4 w-4" />
            <span>Sauvegarder</span>
          </button>
        )}
      </div>

      {/* Résultats principaux */}
      <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
        <div className="bg-blue-50 border border-blue-200 rounded-lg p-4">
          <div className="flex items-center space-x-3">
            <div className="bg-blue-600 p-2 rounded-full">
              <Plane className="h-6 w-6 text-white" />
            </div>
            <div>
              <h4 className="font-semibold text-blue-800">Canadair nécessaires</h4>
              <div className="text-2xl font-bold text-blue-900">
                {formatCalculResult(results.canadairsExact)}
              </div>
              <div className="text-sm text-blue-600">
                Calcul exact: {formatExactValue(results.canadairsExact)}
              </div>
            </div>
          </div>
        </div>

        <div className="bg-green-50 border border-green-200 rounded-lg p-4">
          <div className="flex items-center space-x-3">
            <div className="bg-green-600 p-2 rounded-full">
              <Plane className="h-6 w-6 text-white" />
            </div>
            <div>
              <h4 className="font-semibold text-green-800">Dash nécessaires</h4>
              <div className="text-2xl font-bold text-green-900">
                {formatCalculResult(results.dashsExact)}
              </div>
              <div className="text-sm text-green-600">
                Calcul exact: {formatExactValue(results.dashsExact)}
              </div>
            </div>
          </div>
        </div>
      </div>

      {/* Détails des calculs */}
      <div className="border-t pt-4">
        <h4 className="font-medium text-gray-800 mb-3 flex items-center space-x-2">
          <Calculator className="h-4 w-4" />
          <span>Détail des performances (algorithme appliqué)</span>
        </h4>
        
        <div className="grid grid-cols-1 md:grid-cols-2 gap-4 text-sm">
          <div className="space-y-2">
            <div className="flex justify-between">
              <span className="text-gray-600">Distance plan d'eau:</span>
              <span className="font-medium">{formatDistance(results.distanceEau)}</span>
            </div>
            <div className="flex justify-between">
              <span className="text-gray-600">Largages Canadair (1h):</span>
              <span className="font-medium">{results.largagesCanadairParHeure}</span>
            </div>
            <div className="flex justify-between">
              <span className="text-gray-600">Cycle Canadair:</span>
              <span className="font-medium">{results.tempsCycleCanadair} min</span>
            </div>
            <div className="flex justify-between">
              <span className="text-gray-600">Plan d'eau:</span>
              <span className="font-medium">{results.planEauProche.nom}</span>
            </div>
          </div>

          <div className="space-y-2">
            <div className="flex justify-between">
              <span className="text-gray-600">Distance pélicandrome:</span>
              <span className="font-medium">{formatDistance(results.distanceDash)}</span>
            </div>
            <div className="flex justify-between">
              <span className="text-gray-600">Largages Dash (1h):</span>
              <span className="font-medium">{results.largagesDashParHeure}</span>
            </div>
            <div className="flex justify-between">
              <span className="text-gray-600">Cycle Dash:</span>
              <span className="font-medium">{results.tempsCycleDash} min</span>
            </div>
            <div className="flex justify-between">
              <span className="text-gray-600">Pélicandrome:</span>
              <span className="font-medium">{results.baseDashProche.nom}</span>
            </div>
          </div>
        </div>
      </div>

      {/* Explication de l'algorithme */}
      <div className="bg-gray-50 border border-gray-200 rounded-lg p-4">
        <h4 className="font-medium text-gray-800 mb-2 flex items-center space-x-2">
          <Timer className="h-4 w-4" />
          <span>Méthode de calcul</span>
        </h4>
        <div className="text-sm text-gray-600 space-y-1">
          <div>• Largages par appareil = 1 initial + cycles complets en 1h</div>
          <div>• Cycle = temps vol aller-retour + temps remplissage</div>
          <div>• Nombre d'appareils = arrondi supérieur (objectif ÷ largages par appareil)</div>
          <div>• Facteurs environnementaux et marge de sécurité appliqués</div>
        </div>
      </div>

      {/* Recommandations opérationnelles */}
      <div className="bg-yellow-50 border border-yellow-200 rounded-lg p-4">
        <h4 className="font-medium text-yellow-800 mb-2">Recommandations opérationnelles</h4>
        <ul className="text-sm text-yellow-700 space-y-1">
          <li>• Déclencher {formatCalculResult(results.canadairsExact)} Canadair minimum</li>
          <li>• Prépositionner {formatCalculResult(results.dashsExact)} dash</li>
          <li>• Vérifier disponibilité {results.planEauProche.nom}</li>
          <li>• Alerter pélicandrome {results.baseDashProche.nom}</li>
        </ul>
      </div>
    </div>
  );
}